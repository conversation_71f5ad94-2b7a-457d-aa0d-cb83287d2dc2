"""
Test script to verify MT5 connection and symbol availability
===========================================================

Run this script first to ensure everything is set up correctly before running the main trading strategy.
"""

import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime

def test_mt5_connection():
    """Test MT5 connection and display account information."""
    print("Testing MT5 connection...")
    
    # Initialize MT5
    if not mt5.initialize():
        print("❌ Failed to initialize MT5")
        print("Make sure MetaTrader 5 terminal is running and you are logged in")
        return False
    
    print("✅ MT5 initialized successfully")
    
    # Get account info
    account_info = mt5.account_info()
    if account_info is None:
        print("❌ Failed to get account information")
        return False
    
    print(f"✅ Account Info:")
    print(f"   Login: {account_info.login}")
    print(f"   Server: {account_info.server}")
    print(f"   Balance: {account_info.balance}")
    print(f"   Equity: {account_info.equity}")
    print(f"   Currency: {account_info.currency}")
    
    return True

def test_symbol_availability():
    """Test if the trading symbol is available."""
    symbol = "DEX 900 DOWN Index"
    print(f"\nTesting symbol availability: {symbol}")
    
    # Check if symbol exists
    symbol_info = mt5.symbol_info(symbol)
    if symbol_info is None:
        print(f"❌ Symbol {symbol} not found")
        print("Searching for similar symbols...")
        
        # Search for DEX symbols
        all_symbols = mt5.symbols_get()
        dex_symbols = [s.name for s in all_symbols if 'DEX' in s.name.upper()]
        
        if dex_symbols:
            print("Available DEX symbols:")
            for sym in dex_symbols[:10]:  # Show first 10
                print(f"   - {sym}")
        else:
            print("No DEX symbols found")
        
        return False
    
    print(f"✅ Symbol {symbol} found")
    print(f"   Description: {symbol_info.description}")
    print(f"   Point: {symbol_info.point}")
    print(f"   Digits: {symbol_info.digits}")
    print(f"   Spread: {symbol_info.spread}")
    
    # Enable symbol if not visible
    if not symbol_info.visible:
        print(f"   Symbol not visible, enabling...")
        if mt5.symbol_select(symbol, True):
            print(f"   ✅ Symbol enabled successfully")
        else:
            print(f"   ❌ Failed to enable symbol")
            return False
    
    return True

def test_price_data():
    """Test fetching price data for MACD calculation."""
    symbol = "DEX 900 DOWN Index"
    timeframe = mt5.TIMEFRAME_M15
    
    print(f"\nTesting price data retrieval...")
    
    # Get recent bars
    bars = mt5.copy_rates_from_pos(symbol, timeframe, 0, 50)
    if bars is None or len(bars) == 0:
        print("❌ Failed to get price data")
        return False
    
    print(f"✅ Retrieved {len(bars)} price bars")
    
    # Convert to DataFrame and calculate basic MACD
    df = pd.DataFrame(bars)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    
    # Calculate EMAs for MACD
    df['ema12'] = df['close'].ewm(span=12, adjust=False).mean()
    df['ema26'] = df['close'].ewm(span=26, adjust=False).mean()
    df['macd_line'] = df['ema12'] - df['ema26']
    df['signal_line'] = df['macd_line'].ewm(span=9, adjust=False).mean()
    df['histogram'] = df['macd_line'] - df['signal_line']
    
    # Show latest values
    latest = df.iloc[-1]
    print(f"   Latest bar time: {latest['time']}")
    print(f"   Close price: {latest['close']:.5f}")
    print(f"   MACD line: {latest['macd_line']:.6f}")
    print(f"   Signal line: {latest['signal_line']:.6f}")
    print(f"   Histogram: {latest['histogram']:.6f}")
    
    return True

def test_order_capabilities():
    """Test if we can place orders (without actually placing them)."""
    symbol = "DEX 900 DOWN Index"
    print(f"\nTesting order capabilities...")
    
    # Get current tick
    tick = mt5.symbol_info_tick(symbol)
    if tick is None:
        print("❌ Failed to get current tick data")
        return False
    
    print(f"✅ Current tick data:")
    print(f"   Bid: {tick.bid:.5f}")
    print(f"   Ask: {tick.ask:.5f}")
    print(f"   Spread: {tick.ask - tick.bid:.5f}")
    
    # Check symbol trading permissions
    symbol_info = mt5.symbol_info(symbol)
    if symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_DISABLED:
        print("❌ Trading is disabled for this symbol")
        return False
    
    print(f"✅ Trading is enabled for {symbol}")
    print(f"   Min volume: {symbol_info.volume_min}")
    print(f"   Max volume: {symbol_info.volume_max}")
    print(f"   Volume step: {symbol_info.volume_step}")
    
    return True

def main():
    """Run all tests."""
    print("=" * 60)
    print("MT5 MACD Trading Strategy - Connection Test")
    print("=" * 60)
    
    try:
        # Test MT5 connection
        if not test_mt5_connection():
            return
        
        # Test symbol availability
        if not test_symbol_availability():
            return
        
        # Test price data
        if not test_price_data():
            return
        
        # Test order capabilities
        if not test_order_capabilities():
            return
        
        print("\n" + "=" * 60)
        print("✅ ALL TESTS PASSED!")
        print("You can now run the main trading strategy: python macd_trading_strategy.py")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
    
    finally:
        mt5.shutdown()

if __name__ == "__main__":
    main()
