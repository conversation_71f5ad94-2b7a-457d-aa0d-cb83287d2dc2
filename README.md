# MACD Trading Strategy for MetaTrader 5

This project implements an automated MACD trading strategy for MetaTrader 5 with two types of signals:

1. **Regular Trades**: 0.01 lots, 100-point TP, 50-point SL on MACD line crossovers
2. **Strong Signal Trades**: 0.01 lots, 300-point TP, 150-point SL when crossovers align with trend direction and histogram expansion

## Features

- ✅ Dual-mode MACD strategy (regular + strong signals)
- ✅ Automated trade execution with MT5 integration
- ✅ Comprehensive logging and error handling
- ✅ Configurable parameters
- ✅ Connection testing utilities
- ✅ Real-time signal detection on 15-minute timeframe

## Prerequisites

1. **MetaTrader 5 Terminal**: Must be installed and running
2. **Trading Account**: Demo or live account logged in to MT5
3. **Python 3.8+**: With pip package manager
4. **Symbol Access**: Ensure "DEX 900 DOWN Index" symbol is available in your MT5

## Installation

1. **Clone or download this project** to your local machine

2. **Create and activate virtual environment**:
   ```bash
   python -m venv venv
   
   # On Windows:
   .\venv\Scripts\activate
   
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

## Setup and Testing

### Step 1: Test MT5 Connection

Before running the trading strategy, test your MT5 connection:

```bash
python test_mt5_connection.py
```

This will verify:
- ✅ MT5 terminal connection
- ✅ Account information
- ✅ Symbol availability ("DEX900DOWN")
- ✅ Price data retrieval
- ✅ Trading permissions

### Step 2: Configure Strategy (Optional)

Edit `config.py` to customize:
- Symbol name (if different from "DEX900DOWN")
- Risk parameters (lot size, TP/SL points)
- MACD parameters (periods)
- Trading hours and limits

### Step 3: Run the Trading Strategy

```bash
python macd_trading_strategy.py
```

## Strategy Logic

### Regular Trades
- **Buy Signal**: MACD line crosses above Signal line
- **Sell Signal**: MACD line crosses below Signal line
- **Risk**: 100 points TP, 50 points SL

### Strong Signal Trades
- **Strong Buy**: 
  - Bullish crossover (MACD > Signal)
  - MACD line above zero (bullish trend)
  - Histogram expanding (increasing momentum)
- **Strong Sell**:
  - Bearish crossover (MACD < Signal)  
  - MACD line below zero (bearish trend)
  - Histogram expanding downward (decreasing momentum)
- **Risk**: 300 points TP, 150 points SL

## File Structure

```
McTrader/
├── venv/                          # Virtual environment
├── macd_trading_strategy.py       # Main trading strategy
├── test_mt5_connection.py         # Connection testing utility
├── config.py                      # Configuration parameters
├── requirements.txt               # Python dependencies
├── README.md                      # This file
└── macd_trading.log              # Trading log (created when running)
```

## Monitoring and Logs

The strategy creates detailed logs in `macd_trading.log` including:
- Signal detections
- Trade executions
- Errors and warnings
- MACD indicator values

Monitor the console output for real-time updates.

## Safety Features

- ✅ Symbol validation before trading
- ✅ Connection error handling
- ✅ Trade execution verification
- ✅ Comprehensive logging
- ✅ Graceful shutdown on interruption

## Important Notes

### Symbol Name
- Default symbol: "DEX 900 DOWN Index"
- This is the correct symbol name as confirmed by the user
- Run the test script to see available symbols

### Risk Management
- **Start with demo account** to test the strategy
- Monitor initial trades closely
- Adjust lot sizes and TP/SL according to your risk tolerance
- Consider market conditions and volatility

### Trading Hours
- Strategy runs 24/7 by default
- Configure trading hours in `config.py` if needed
- Be aware of market sessions and volatility patterns

## Troubleshooting

### Common Issues

1. **"Failed to initialize MT5"**
   - Ensure MT5 terminal is running
   - Check if you're logged into your trading account
   - Restart MT5 terminal if needed

2. **"Symbol DEX 900 DOWN Index not found"**
   - Check if the symbol is available in your MT5 Market Watch
   - Ensure the symbol is enabled/visible
   - Enable the symbol in MT5 Market Watch

3. **"Failed to get price data"**
   - Ensure symbol is enabled and visible
   - Check internet connection
   - Verify market is open for the symbol

4. **Trade execution failures**
   - Check account balance and margin
   - Verify trading permissions for the symbol
   - Ensure lot size meets minimum requirements

### Getting Help

1. Run `python test_mt5_connection.py` to diagnose issues
2. Check the `macd_trading.log` file for detailed error messages
3. Verify MT5 terminal is properly configured and connected

## Disclaimer

This trading strategy is for educational and testing purposes. Always:
- Test thoroughly on demo accounts first
- Understand the risks involved in automated trading
- Monitor the strategy's performance regularly
- Never risk more than you can afford to lose
- Consider market conditions and news events

**Trading involves substantial risk and is not suitable for all investors.**
