@echo off
echo ============================================================
echo MACD Trading System - MT5 Connection Test
echo ============================================================
echo.
echo IMPORTANT: Make sure MetaTrader 5 is running and logged in
echo before running this test!
echo.
echo This will test:
echo - MT5 terminal connection
echo - Account information
echo - Symbol availability (DEX 900 DOWN Index)
echo - Price data retrieval
echo - Trading permissions
echo.
pause

REM Activate virtual environment and run MT5 connection test
call venv\Scripts\activate.bat
python test_mt5_connection.py

echo.
echo ============================================================
echo Connection test completed. Check results above.
echo If all tests passed, you can run the trading strategy.
echo ============================================================
pause
