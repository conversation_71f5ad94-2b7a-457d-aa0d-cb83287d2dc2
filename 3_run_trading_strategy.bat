@echo off
echo ============================================================
echo MACD Trading System - Start Trading Strategy
echo ============================================================
echo.
echo WARNING: This will start automated trading!
echo.
echo Make sure you have:
echo 1. Tested the system (run 1_test_system.bat)
echo 2. Tested MT5 connection (run 2_test_mt5_connection.bat)
echo 3. MetaTrader 5 is running and logged in
echo 4. You are using a DEMO account for testing
echo.
echo The strategy will:
echo - Monitor DEX 900 DOWN Index on 15-minute timeframe
echo - Execute regular trades (100 TP / 50 SL) on MACD crossovers
echo - Execute strong trades (300 TP / 150 SL) on trend-aligned signals
echo - Log all activity to macd_trading.log
echo.
echo Press Ctrl+C to stop the strategy at any time.
echo.
set /p confirm="Are you sure you want to start trading? (y/N): "
if /i not "%confirm%"=="y" (
    echo Trading cancelled.
    pause
    exit /b
)

echo.
echo Starting MACD Trading Strategy...
echo ============================================================

REM Activate virtual environment and run trading strategy
call venv\Scripts\activate.bat
python macd_trading_strategy.py

echo.
echo ============================================================
echo Trading strategy stopped.
echo Check macd_trading.log for detailed logs.
echo ============================================================
pause
