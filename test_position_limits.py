"""
Test Position Limits for MACD Trading System
============================================

This script tests that the system only allows one regular trade and one strong signal trade at a time.
"""

import MetaTrader5 as mt5
import time
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('position_limits_test.log'),
        logging.StreamHandler()
    ]
)

class PositionLimitTester:
    def __init__(self):
        self.SYMBOL = "DEX 900 DOWN Index"
        self.LOT_SIZE = 0.01
        self.REGULAR_TP = 10000
        self.REGULAR_SL = 6000
        self.STRONG_TP = 30000
        self.STRONG_SL = 15000
        self.MAGIC_NUMBER = 202304
        
        # Position tracking
        self.regular_position_open = False
        self.strong_position_open = False
        
        # Initialize MT5
        self.initialize_mt5()
    
    def initialize_mt5(self):
        """Initialize MT5 connection."""
        if not mt5.initialize():
            logging.error("Failed to initialize MT5")
            raise Exception("MT5 initialization failed")
        
        symbol_info = mt5.symbol_info(self.SYMBOL)
        if symbol_info is None:
            logging.error(f"Symbol {self.SYMBOL} not found")
            raise Exception(f"Symbol {self.SYMBOL} not available")
        
        if not symbol_info.visible:
            if not mt5.symbol_select(self.SYMBOL, True):
                logging.error(f"Failed to enable symbol {self.SYMBOL}")
                raise Exception(f"Cannot enable symbol {self.SYMBOL}")
        
        logging.info(f"MT5 initialized successfully for {self.SYMBOL}")
    
    def check_existing_positions(self):
        """Check for existing positions and update position tracking."""
        try:
            positions = mt5.positions_get(symbol=self.SYMBOL)
            if positions is None:
                self.regular_position_open = False
                self.strong_position_open = False
                return
            
            # Reset position flags
            self.regular_position_open = False
            self.strong_position_open = False
            
            # Check each position
            for position in positions:
                if position.magic == self.MAGIC_NUMBER:
                    comment = position.comment.lower()
                    if "regular" in comment:
                        self.regular_position_open = True
                        logging.info(f"Regular position found: {position.ticket}")
                    elif "strong" in comment:
                        self.strong_position_open = True
                        logging.info(f"Strong position found: {position.ticket}")
            
        except Exception as e:
            logging.error(f"Error checking existing positions: {e}")
            self.regular_position_open = False
            self.strong_position_open = False
    
    def attempt_trade(self, direction, signal_strength):
        """Attempt to open a trade and return success/failure."""
        try:
            # Check if we already have a position of this type open
            if signal_strength == "regular" and self.regular_position_open:
                logging.info(f"✅ CORRECTLY BLOCKED: {signal_strength} {direction} trade - regular position already open")
                return False
            elif signal_strength == "strong" and self.strong_position_open:
                logging.info(f"✅ CORRECTLY BLOCKED: {signal_strength} {direction} trade - strong position already open")
                return False
            
            # Get current price
            tick = mt5.symbol_info_tick(self.SYMBOL)
            if tick is None:
                logging.error("Failed to get current tick data")
                return False
            
            # Set TP/SL based on signal strength
            if signal_strength == "strong":
                tp_points = self.STRONG_TP
                sl_points = self.STRONG_SL
            else:
                tp_points = self.REGULAR_TP
                sl_points = self.REGULAR_SL
            
            # Calculate prices
            point = mt5.symbol_info(self.SYMBOL).point
            if direction == "buy":
                order_type = mt5.ORDER_TYPE_BUY
                price = tick.ask
                sl = price - sl_points * point
                tp = price + tp_points * point
            else:
                order_type = mt5.ORDER_TYPE_SELL
                price = tick.bid
                sl = price + sl_points * point
                tp = price - tp_points * point
            
            # Create trade request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": self.SYMBOL,
                "volume": self.LOT_SIZE,
                "type": order_type,
                "price": price,
                "sl": sl,
                "tp": tp,
                "deviation": 20,
                "magic": self.MAGIC_NUMBER,
                "comment": f"TEST {signal_strength} {direction}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_FOK,
            }
            
            # Send order
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logging.error(f"Trade failed: {result.retcode} - {result.comment}")
                return False
            
            logging.info(f"✅ TRADE OPENED: {signal_strength} {direction} - Ticket: {result.order}")
            
            # Update position tracking
            if signal_strength == "regular":
                self.regular_position_open = True
            elif signal_strength == "strong":
                self.strong_position_open = True
            
            return True
            
        except Exception as e:
            logging.error(f"Error opening trade: {e}")
            return False
    
    def close_all_test_positions(self):
        """Close all test positions."""
        logging.info("Closing all test positions...")
        
        positions = mt5.positions_get(symbol=self.SYMBOL)
        if not positions:
            logging.info("No positions to close")
            return
        
        for position in positions:
            if position.magic == self.MAGIC_NUMBER:
                if position.type == mt5.POSITION_TYPE_BUY:
                    order_type = mt5.ORDER_TYPE_SELL
                    price = mt5.symbol_info_tick(self.SYMBOL).bid
                else:
                    order_type = mt5.ORDER_TYPE_BUY
                    price = mt5.symbol_info_tick(self.SYMBOL).ask
                
                close_request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": self.SYMBOL,
                    "volume": position.volume,
                    "type": order_type,
                    "position": position.ticket,
                    "price": price,
                    "deviation": 20,
                    "magic": self.MAGIC_NUMBER,
                    "comment": "TEST CLOSE",
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": mt5.ORDER_FILLING_FOK,
                }
                
                result = mt5.order_send(close_request)
                if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                    logging.info(f"✅ Position {position.ticket} closed successfully")
                else:
                    logging.error(f"❌ Failed to close position {position.ticket}")
    
    def run_position_limit_tests(self):
        """Run comprehensive position limit tests."""
        logging.info("=" * 60)
        logging.info("STARTING POSITION LIMIT TESTS")
        logging.info("=" * 60)
        
        # Clean slate - close any existing positions
        self.close_all_test_positions()
        time.sleep(2)
        self.check_existing_positions()
        
        test_results = []
        
        # Test 1: Open first regular trade (should succeed)
        logging.info("\n" + "=" * 40)
        logging.info("TEST 1: FIRST REGULAR TRADE (SHOULD SUCCEED)")
        logging.info("=" * 40)
        result1 = self.attempt_trade("buy", "regular")
        test_results.append(("First Regular Trade", result1, True))  # Expected: True
        time.sleep(2)
        self.check_existing_positions()
        
        # Test 2: Try to open second regular trade (should be blocked)
        logging.info("\n" + "=" * 40)
        logging.info("TEST 2: SECOND REGULAR TRADE (SHOULD BE BLOCKED)")
        logging.info("=" * 40)
        result2 = self.attempt_trade("sell", "regular")
        test_results.append(("Second Regular Trade", result2, False))  # Expected: False
        time.sleep(2)
        
        # Test 3: Open first strong trade (should succeed)
        logging.info("\n" + "=" * 40)
        logging.info("TEST 3: FIRST STRONG TRADE (SHOULD SUCCEED)")
        logging.info("=" * 40)
        result3 = self.attempt_trade("buy", "strong")
        test_results.append(("First Strong Trade", result3, True))  # Expected: True
        time.sleep(2)
        self.check_existing_positions()
        
        # Test 4: Try to open second strong trade (should be blocked)
        logging.info("\n" + "=" * 40)
        logging.info("TEST 4: SECOND STRONG TRADE (SHOULD BE BLOCKED)")
        logging.info("=" * 40)
        result4 = self.attempt_trade("sell", "strong")
        test_results.append(("Second Strong Trade", result4, False))  # Expected: False
        time.sleep(2)
        
        # Clean up
        logging.info("\n" + "=" * 40)
        logging.info("CLEANING UP TEST POSITIONS")
        logging.info("=" * 40)
        self.close_all_test_positions()
        
        # Print summary
        logging.info("\n" + "=" * 60)
        logging.info("POSITION LIMIT TEST SUMMARY")
        logging.info("=" * 60)
        
        passed = 0
        for test_name, actual_result, expected_result in test_results:
            if actual_result == expected_result:
                status = "✅ PASSED"
                passed += 1
            else:
                status = "❌ FAILED"
            logging.info(f"{test_name}: {status} (Expected: {expected_result}, Got: {actual_result})")
        
        logging.info(f"\nOverall Result: {passed}/{len(test_results)} tests passed")
        
        if passed == len(test_results):
            logging.info("🎉 ALL POSITION LIMIT TESTS PASSED!")
            logging.info("The system correctly limits positions to one regular and one strong trade.")
        else:
            logging.info("⚠️ Some tests failed. Position limiting may not be working correctly.")
        
        return passed == len(test_results)

def main():
    """Main entry point."""
    try:
        tester = PositionLimitTester()
        success = tester.run_position_limit_tests()
        
        if success:
            print("\n🎉 SUCCESS: All position limit tests passed!")
            print("The system correctly prevents multiple trades of the same type.")
        else:
            print("\n⚠️ WARNING: Some position limit tests failed.")
            print("Check the logs for details.")
        
    except Exception as e:
        logging.error(f"Test failed with error: {e}")
        print(f"❌ ERROR: {e}")
    
    finally:
        mt5.shutdown()
        logging.info("MT5 connection closed")

if __name__ == "__main__":
    main()
