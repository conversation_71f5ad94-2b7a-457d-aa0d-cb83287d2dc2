"""
Comprehensive Error Testing Script for MACD Trading System
=========================================================

This script tests all components of the MACD trading system for errors:
- Syntax errors in Python files
- Import errors
- Basic functionality tests
- Configuration validation
"""

import sys
import os
import ast
import importlib.util
import traceback
from pathlib import Path

class SystemTester:
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.passed_tests = []
        
    def log_error(self, test_name, error_msg):
        """Log an error."""
        self.errors.append(f"❌ {test_name}: {error_msg}")
        print(f"❌ {test_name}: {error_msg}")
    
    def log_warning(self, test_name, warning_msg):
        """Log a warning."""
        self.warnings.append(f"⚠️  {test_name}: {warning_msg}")
        print(f"⚠️  {test_name}: {warning_msg}")
    
    def log_pass(self, test_name):
        """Log a passed test."""
        self.passed_tests.append(f"✅ {test_name}")
        print(f"✅ {test_name}")
    
    def test_file_syntax(self, filepath):
        """Test Python file for syntax errors."""
        test_name = f"Syntax check: {filepath}"
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                source = f.read()
            
            # Compile to check syntax
            ast.parse(source, filename=filepath)
            self.log_pass(test_name)
            return True
            
        except SyntaxError as e:
            self.log_error(test_name, f"Syntax error at line {e.lineno}: {e.msg}")
            return False
        except Exception as e:
            self.log_error(test_name, f"Error reading file: {e}")
            return False
    
    def test_imports(self, filepath):
        """Test if all imports in a file work."""
        test_name = f"Import check: {filepath}"
        try:
            # Load the module spec
            spec = importlib.util.spec_from_file_location("test_module", filepath)
            if spec is None:
                self.log_error(test_name, "Could not load module spec")
                return False
            
            # Try to create the module (this will test imports)
            module = importlib.util.module_from_spec(spec)
            
            # Execute the module to test imports
            spec.loader.exec_module(module)
            self.log_pass(test_name)
            return True
            
        except ImportError as e:
            self.log_error(test_name, f"Import error: {e}")
            return False
        except Exception as e:
            self.log_error(test_name, f"Error: {e}")
            return False
    
    def test_config_file(self):
        """Test configuration file."""
        test_name = "Configuration validation"
        try:
            import config
            
            # Check required attributes
            required_attrs = [
                'SYMBOL', 'LOT_SIZE', 'REGULAR_TP_POINTS', 'REGULAR_SL_POINTS',
                'STRONG_TP_POINTS', 'STRONG_SL_POINTS', 'MACD_FAST_PERIOD',
                'MACD_SLOW_PERIOD', 'MACD_SIGNAL_PERIOD'
            ]
            
            missing_attrs = []
            for attr in required_attrs:
                if not hasattr(config, attr):
                    missing_attrs.append(attr)
            
            if missing_attrs:
                self.log_error(test_name, f"Missing configuration attributes: {missing_attrs}")
                return False
            
            # Validate values
            if config.LOT_SIZE <= 0:
                self.log_error(test_name, "LOT_SIZE must be positive")
                return False
            
            if config.REGULAR_TP_POINTS <= 0 or config.REGULAR_SL_POINTS <= 0:
                self.log_error(test_name, "TP/SL points must be positive")
                return False
            
            if config.MACD_FAST_PERIOD >= config.MACD_SLOW_PERIOD:
                self.log_error(test_name, "MACD fast period must be less than slow period")
                return False
            
            self.log_pass(test_name)
            return True
            
        except Exception as e:
            self.log_error(test_name, f"Error: {e}")
            return False
    
    def test_requirements_file(self):
        """Test if requirements.txt exists and is valid."""
        test_name = "Requirements file check"
        try:
            if not os.path.exists('requirements.txt'):
                self.log_error(test_name, "requirements.txt not found")
                return False
            
            with open('requirements.txt', 'r') as f:
                requirements = f.read().strip()
            
            if not requirements:
                self.log_error(test_name, "requirements.txt is empty")
                return False
            
            # Check for essential packages
            essential_packages = ['MetaTrader5', 'pandas']
            for package in essential_packages:
                if package not in requirements:
                    self.log_warning(test_name, f"Essential package '{package}' not found in requirements.txt")
            
            self.log_pass(test_name)
            return True
            
        except Exception as e:
            self.log_error(test_name, f"Error: {e}")
            return False
    
    def test_virtual_environment(self):
        """Test if virtual environment is set up correctly."""
        test_name = "Virtual environment check"
        try:
            # Check if we're in a virtual environment
            if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
                self.log_pass(test_name + " (Active)")
                return True
            else:
                # Check if venv directory exists
                if os.path.exists('venv'):
                    self.log_warning(test_name, "Virtual environment exists but not activated")
                    return True
                else:
                    self.log_error(test_name, "Virtual environment not found")
                    return False
                    
        except Exception as e:
            self.log_error(test_name, f"Error: {e}")
            return False
    
    def test_file_structure(self):
        """Test if all required files exist."""
        test_name = "File structure check"
        required_files = [
            'macd_trading_strategy.py',
            'test_mt5_connection.py',
            'config.py',
            'requirements.txt',
            'README.md'
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            self.log_error(test_name, f"Missing files: {missing_files}")
            return False
        
        self.log_pass(test_name)
        return True
    
    def run_all_tests(self):
        """Run all system tests."""
        print("=" * 60)
        print("MACD Trading System - Error Testing")
        print("=" * 60)
        
        # Test file structure
        self.test_file_structure()
        
        # Test virtual environment
        self.test_virtual_environment()
        
        # Test requirements file
        self.test_requirements_file()
        
        # Test Python file syntax
        python_files = [
            'macd_trading_strategy.py',
            'test_mt5_connection.py',
            'config.py'
        ]
        
        syntax_ok = True
        for file in python_files:
            if os.path.exists(file):
                if not self.test_file_syntax(file):
                    syntax_ok = False
        
        # Test imports (only if syntax is OK)
        if syntax_ok:
            for file in python_files:
                if os.path.exists(file):
                    self.test_imports(file)
        else:
            print("⚠️  Skipping import tests due to syntax errors")
        
        # Test configuration
        self.test_config_file()
        
        # Print summary
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        
        print(f"✅ Passed: {len(self.passed_tests)}")
        print(f"⚠️  Warnings: {len(self.warnings)}")
        print(f"❌ Errors: {len(self.errors)}")
        
        if self.errors:
            print("\nERRORS FOUND:")
            for error in self.errors:
                print(f"  {error}")
        
        if self.warnings:
            print("\nWARNINGS:")
            for warning in self.warnings:
                print(f"  {warning}")
        
        if not self.errors:
            print("\n🎉 ALL CRITICAL TESTS PASSED!")
            print("System appears to be ready for use.")
        else:
            print(f"\n❌ {len(self.errors)} CRITICAL ERRORS FOUND!")
            print("Please fix these errors before running the trading system.")
        
        return len(self.errors) == 0

def main():
    """Main entry point."""
    tester = SystemTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n" + "=" * 60)
        print("NEXT STEPS:")
        print("1. Run: python test_mt5_connection.py")
        print("2. If connection test passes, run: python macd_trading_strategy.py")
        print("=" * 60)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
