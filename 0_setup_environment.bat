@echo off
echo ============================================================
echo MACD Trading System - Environment Setup
echo ============================================================
echo.
echo This will set up the Python virtual environment and install
echo all required dependencies for the MACD trading system.
echo.
echo Steps:
echo 1. Create virtual environment (if not exists)
echo 2. Activate virtual environment
echo 3. Install required packages (MetaTrader5, pandas, numpy)
echo.
pause

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ERROR: Failed to create virtual environment.
        echo Make sure Python is installed and accessible.
        pause
        exit /b 1
    )
    echo Virtual environment created successfully.
) else (
    echo Virtual environment already exists.
)

echo.
echo Activating virtual environment and installing dependencies...
call venv\Scripts\activate.bat

echo Installing required packages...
pip install --upgrade pip
pip install -r requirements.txt

if errorlevel 1 (
    echo ERROR: Failed to install dependencies.
    pause
    exit /b 1
)

echo.
echo ============================================================
echo Environment setup completed successfully!
echo.
echo Next steps:
echo 1. Run: 1_test_system.bat (to test for errors)
echo 2. Run: 2_test_mt5_connection.bat (to test MT5 connection)
echo 3. Run: 3_run_trading_strategy.bat (to start trading)
echo ============================================================
pause
