"""
MACD Trading Strategy for MetaTrader 5
=====================================

This script implements a dual-mode MACD trading strategy:
1. Regular trades: 0.01 lots, 100-point TP, 50-point SL on MACD crossovers
2. Strong signal trades: 0.01 lots, 300-point TP, 150-point SL with trend alignment

Author: Trading Bot
Date: 2025-07-11
"""

import MetaTrader5 as mt5
import pandas as pd
import time
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('macd_trading.log'),
        logging.StreamHandler()
    ]
)

class MACDTradingStrategy:
    def __init__(self):
        # Strategy parameters
        self.SYMBOL = "DEX 900 DOWN Index"  # MT5 symbol name
        self.TIMEFRAME = mt5.TIMEFRAME_M15  # 15-minute timeframe
        self.LOT_SIZE = 0.01
        self.REGULAR_TP = 2000  # Points for regular trades (adjusted for min stops level)
        self.REGULAR_SL = 1100  # Points for regular trades (adjusted for min stops level)
        self.STRONG_TP = 3000   # Points for strong trades (adjusted for min stops level)
        self.STRONG_SL = 1500   # Points for strong trades (adjusted for min stops level)
        self.MAGIC_NUMBER = 202304
        
        # State tracking
        self.prev_histogram = None
        self.prev_macd_line = None
        self.prev_signal_line = None
        self.mt5_connected = False

        # Try to initialize MT5 connection (but don't fail if it's not available)
        self.try_initialize_mt5()
    
    def try_initialize_mt5(self):
        """Try to initialize MT5 connection and verify symbol availability."""
        try:
            if not mt5.initialize():
                logging.warning("Failed to initialize MT5. Is the terminal running?")
                self.mt5_connected = False
                return False

            # Check if symbol exists
            symbol_info = mt5.symbol_info(self.SYMBOL)
            if symbol_info is None:
                logging.error(f"Symbol {self.SYMBOL} not found")
                available_symbols = [s.name for s in mt5.symbols_get() if 'DEX' in s.name.upper()]
                if available_symbols:
                    logging.info(f"Available DEX symbols: {available_symbols}")
                mt5.shutdown()
                self.mt5_connected = False
                return False

            # Enable symbol if not visible
            if not symbol_info.visible:
                if not mt5.symbol_select(self.SYMBOL, True):
                    logging.error(f"Failed to enable symbol {self.SYMBOL}")
                    mt5.shutdown()
                    self.mt5_connected = False
                    return False

            logging.info(f"MT5 initialized successfully. Trading symbol: {self.SYMBOL}")
            logging.info(f"Account info: {mt5.account_info()}")

            # Check minimum stops level
            stops_level = symbol_info.trade_stops_level
            logging.info(f"Symbol minimum stops level: {stops_level} points")
            if self.REGULAR_SL < stops_level or self.STRONG_SL < stops_level:
                logging.warning(f"Stop Loss levels may be too small. Minimum required: {stops_level} points")

            self.mt5_connected = True
            return True

        except Exception as e:
            logging.error(f"Error initializing MT5: {e}")
            self.mt5_connected = False
            return False
    
    def get_macd(self, fast=12, slow=26, signal=9):
        """Fetch price data and calculate MACD indicators."""
        try:
            bars = mt5.copy_rates_from_pos(self.SYMBOL, self.TIMEFRAME, 0, 100)
            if bars is None or len(bars) == 0:
                logging.error("Failed to get price data")
                return None
            
            df = pd.DataFrame(bars)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            
            # Calculate EMAs
            df['ema12'] = df['close'].ewm(span=fast, adjust=False).mean()
            df['ema26'] = df['close'].ewm(span=slow, adjust=False).mean()
            df['macd_line'] = df['ema12'] - df['ema26']
            df['signal_line'] = df['macd_line'].ewm(span=signal, adjust=False).mean()
            df['histogram'] = df['macd_line'] - df['signal_line']
            
            return df.iloc[-2:]  # Return last 2 rows for crossover detection
            
        except Exception as e:
            logging.error(f"Error calculating MACD: {e}")
            return None
    
    def detect_crossover(self, current_data):
        """Detect MACD crossovers and determine signal strength."""
        if len(current_data) < 2:
            return None, None
        
        prev_row = current_data.iloc[0]
        curr_row = current_data.iloc[1]
        
        prev_macd = prev_row['macd_line']
        prev_signal = prev_row['signal_line']
        prev_hist = prev_row['histogram']
        
        curr_macd = curr_row['macd_line']
        curr_signal = curr_row['signal_line']
        curr_hist = curr_row['histogram']
        
        # Detect crossovers
        bullish_cross = (prev_macd <= prev_signal) and (curr_macd > curr_signal)
        bearish_cross = (prev_macd >= prev_signal) and (curr_macd < curr_signal)
        
        if not (bullish_cross or bearish_cross):
            return None, None
        
        # Determine signal type
        signal_type = "buy" if bullish_cross else "sell"
        
        # Check for strong signal conditions
        if bullish_cross:
            # Strong buy: bullish crossover + MACD above zero + histogram expanding
            strong_signal = (curr_macd > 0) and (curr_hist > prev_hist)
        else:
            # Strong sell: bearish crossover + MACD below zero + histogram expanding downward
            strong_signal = (curr_macd < 0) and (curr_hist < prev_hist)
        
        signal_strength = "strong" if strong_signal else "regular"
        
        logging.info(f"{signal_strength.upper()} {signal_type.upper()} signal detected")
        logging.info(f"MACD: {curr_macd:.6f}, Signal: {curr_signal:.6f}, Histogram: {curr_hist:.6f}")
        
        return signal_type, signal_strength
    
    def open_trade(self, direction, signal_strength):
        """Open a trade with specified parameters."""
        try:
            point = mt5.symbol_info(self.SYMBOL).point
            tick = mt5.symbol_info_tick(self.SYMBOL)
            
            if tick is None:
                logging.error("Failed to get current tick data")
                return False
            
            # Set TP/SL based on signal strength
            if signal_strength == "strong":
                tp_points = self.STRONG_TP
                sl_points = self.STRONG_SL
            else:
                tp_points = self.REGULAR_TP
                sl_points = self.REGULAR_SL
            
            # Determine order parameters
            if direction == "buy":
                order_type = mt5.ORDER_TYPE_BUY
                price = tick.ask
                sl = price - sl_points * point
                tp = price + tp_points * point
            else:  # sell
                order_type = mt5.ORDER_TYPE_SELL
                price = tick.bid
                sl = price + sl_points * point
                tp = price - tp_points * point
            
            # Create trade request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": self.SYMBOL,
                "volume": self.LOT_SIZE,
                "type": order_type,
                "price": price,
                "sl": sl,
                "tp": tp,
                "deviation": 20,
                "magic": self.MAGIC_NUMBER,
                "comment": f"MACD {signal_strength} {direction}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_FOK,
            }
            
            # Send order
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logging.error(f"Trade failed: {result.retcode} - {result.comment}")
                return False
            
            logging.info(f"Trade opened successfully: {direction.upper()} {self.LOT_SIZE} lots")
            logging.info(f"Entry: {price}, SL: {sl}, TP: {tp}")
            logging.info(f"Order ticket: {result.order}")
            
            return True
            
        except Exception as e:
            logging.error(f"Error opening trade: {e}")
            return False
    
    def run(self):
        """Main trading loop."""
        logging.info("Starting MACD trading strategy...")
        logging.info(f"Monitoring {self.SYMBOL} on {self.TIMEFRAME} timeframe")

        try:
            while True:
                # Check MT5 connection
                if not self.mt5_connected:
                    logging.info("MT5 not connected. Attempting to connect...")
                    if self.try_initialize_mt5():
                        logging.info("MT5 connection established successfully!")
                    else:
                        logging.warning("MT5 connection failed. Retrying in 30 seconds...")
                        time.sleep(30)
                        continue

                # Get latest MACD data
                macd_data = self.get_macd()
                if macd_data is None:
                    logging.warning("No MACD data available, retrying in 10 seconds...")
                    # Check if it's a connection issue
                    if not mt5.terminal_info():
                        logging.warning("MT5 connection lost. Will attempt to reconnect...")
                        self.mt5_connected = False
                    time.sleep(10)
                    continue

                # Detect trading signals
                signal_type, signal_strength = self.detect_crossover(macd_data)

                if signal_type and signal_strength:
                    # Execute trade
                    success = self.open_trade(signal_type, signal_strength)
                    if success:
                        logging.info(f"Trade executed: {signal_strength} {signal_type}")
                    else:
                        logging.error(f"Failed to execute trade: {signal_strength} {signal_type}")
                        # Check if it's a connection issue
                        if not mt5.terminal_info():
                            logging.warning("MT5 connection lost during trade execution. Will attempt to reconnect...")
                            self.mt5_connected = False

                # Wait before next check
                time.sleep(60)  # Check every 60 seconds

        except KeyboardInterrupt:
            logging.info("Trading strategy stopped by user")
        except Exception as e:
            logging.error(f"Unexpected error in main loop: {e}")
        finally:
            if self.mt5_connected:
                mt5.shutdown()
            logging.info("MT5 connection closed")

def main():
    """Main entry point."""
    try:
        logging.info("Initializing MACD Trading Strategy...")
        strategy = MACDTradingStrategy()
        strategy.run()
    except Exception as e:
        logging.error(f"Failed to start trading strategy: {e}")
        print(f"Error: {e}")
        print("The system will keep trying to connect to MT5...")
        print("Make sure MetaTrader 5 is running and try again.")

if __name__ == "__main__":
    main()
