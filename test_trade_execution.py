"""
Test Trade Execution for MACD Trading System
============================================

This script tests if the system can successfully open trades with specified TP and SL.
It will attempt to place both regular and strong signal trades to verify functionality.
"""

import MetaTrader5 as mt5
import time
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trade_execution_test.log'),
        logging.StreamHandler()
    ]
)

class TradeExecutionTester:
    def __init__(self):
        self.SYMBOL = "DEX 900 DOWN Index"
        self.LOT_SIZE = 0.01
        self.REGULAR_TP = 10000  # Points (user specified)
        self.REGULAR_SL = 6000   # Points (user specified)
        self.STRONG_TP = 30000   # Points (user specified)
        self.STRONG_SL = 15000   # Points (user specified)
        self.MAGIC_NUMBER = 202304
        
        # Initialize MT5
        self.initialize_mt5()
    
    def initialize_mt5(self):
        """Initialize MT5 connection."""
        if not mt5.initialize():
            logging.error("Failed to initialize MT5")
            raise Exception("MT5 initialization failed")
        
        # Check symbol
        symbol_info = mt5.symbol_info(self.SYMBOL)
        if symbol_info is None:
            logging.error(f"Symbol {self.SYMBOL} not found")
            raise Exception(f"Symbol {self.SYMBOL} not available")
        
        # Enable symbol if needed
        if not symbol_info.visible:
            if not mt5.symbol_select(self.SYMBOL, True):
                logging.error(f"Failed to enable symbol {self.SYMBOL}")
                raise Exception(f"Cannot enable symbol {self.SYMBOL}")
        
        logging.info(f"MT5 initialized successfully for {self.SYMBOL}")
        account_info = mt5.account_info()
        logging.info(f"Account: {account_info.name} | Balance: {account_info.balance} {account_info.currency}")
    
    def get_current_price_info(self):
        """Get current price information."""
        tick = mt5.symbol_info_tick(self.SYMBOL)
        symbol_info = mt5.symbol_info(self.SYMBOL)
        
        if tick is None or symbol_info is None:
            return None
        
        return {
            'bid': tick.bid,
            'ask': tick.ask,
            'spread': tick.ask - tick.bid,
            'point': symbol_info.point,
            'digits': symbol_info.digits
        }
    
    def test_trade_execution(self, direction, signal_strength):
        """Test opening a trade with specified parameters."""
        logging.info(f"Testing {signal_strength} {direction} trade execution...")
        
        try:
            # Get current price info
            price_info = self.get_current_price_info()
            if price_info is None:
                logging.error("Failed to get current price information")
                return False
            
            logging.info(f"Current prices - Bid: {price_info['bid']}, Ask: {price_info['ask']}, Spread: {price_info['spread']:.5f}")
            
            # Set TP/SL based on signal strength
            if signal_strength == "strong":
                tp_points = self.STRONG_TP
                sl_points = self.STRONG_SL
            else:
                tp_points = self.REGULAR_TP
                sl_points = self.REGULAR_SL
            
            # Calculate entry price, TP, and SL
            if direction == "buy":
                order_type = mt5.ORDER_TYPE_BUY
                entry_price = price_info['ask']
                sl_price = entry_price - sl_points * price_info['point']
                tp_price = entry_price + tp_points * price_info['point']
            else:  # sell
                order_type = mt5.ORDER_TYPE_SELL
                entry_price = price_info['bid']
                sl_price = entry_price + sl_points * price_info['point']
                tp_price = entry_price - tp_points * price_info['point']
            
            logging.info(f"Trade parameters:")
            logging.info(f"  Direction: {direction.upper()}")
            logging.info(f"  Entry Price: {entry_price}")
            logging.info(f"  Stop Loss: {sl_price} ({sl_points} points)")
            logging.info(f"  Take Profit: {tp_price} ({tp_points} points)")
            logging.info(f"  Volume: {self.LOT_SIZE} lots")
            
            # Create trade request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": self.SYMBOL,
                "volume": self.LOT_SIZE,
                "type": order_type,
                "price": entry_price,
                "sl": sl_price,
                "tp": tp_price,
                "deviation": 20,
                "magic": self.MAGIC_NUMBER,
                "comment": f"TEST {signal_strength} {direction}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_FOK,
            }
            
            # Send the order
            logging.info("Sending trade order...")
            result = mt5.order_send(request)
            
            if result is None:
                logging.error("Order send failed - no result returned")
                return False
            
            # Check result
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logging.error(f"Trade failed with return code: {result.retcode}")
                logging.error(f"Error description: {result.comment}")
                return False
            
            # Trade successful
            logging.info("✅ TRADE EXECUTED SUCCESSFULLY!")
            logging.info(f"Order ticket: {result.order}")
            logging.info(f"Deal ticket: {result.deal}")
            logging.info(f"Actual entry price: {result.price}")
            logging.info(f"Volume filled: {result.volume}")
            
            # Wait a moment then check position
            time.sleep(2)
            positions = mt5.positions_get(symbol=self.SYMBOL)
            if positions:
                for pos in positions:
                    if pos.ticket == result.order:
                        logging.info(f"Position confirmed:")
                        logging.info(f"  Position ID: {pos.ticket}")
                        logging.info(f"  Type: {'BUY' if pos.type == 0 else 'SELL'}")
                        logging.info(f"  Volume: {pos.volume}")
                        logging.info(f"  Open Price: {pos.price_open}")
                        logging.info(f"  Stop Loss: {pos.sl}")
                        logging.info(f"  Take Profit: {pos.tp}")
                        logging.info(f"  Current Profit: {pos.profit}")
                        break
            
            return True
            
        except Exception as e:
            logging.error(f"Error during trade execution: {e}")
            return False
    
    def close_all_test_positions(self):
        """Close all test positions."""
        logging.info("Closing all test positions...")
        
        positions = mt5.positions_get(symbol=self.SYMBOL)
        if not positions:
            logging.info("No positions to close")
            return
        
        for position in positions:
            if position.magic == self.MAGIC_NUMBER:
                # Create close request
                if position.type == mt5.POSITION_TYPE_BUY:
                    order_type = mt5.ORDER_TYPE_SELL
                    price = mt5.symbol_info_tick(self.SYMBOL).bid
                else:
                    order_type = mt5.ORDER_TYPE_BUY
                    price = mt5.symbol_info_tick(self.SYMBOL).ask
                
                close_request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": self.SYMBOL,
                    "volume": position.volume,
                    "type": order_type,
                    "position": position.ticket,
                    "price": price,
                    "deviation": 20,
                    "magic": self.MAGIC_NUMBER,
                    "comment": "TEST CLOSE",
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": mt5.ORDER_FILLING_FOK,
                }
                
                result = mt5.order_send(close_request)
                if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                    logging.info(f"✅ Position {position.ticket} closed successfully")
                else:
                    logging.error(f"❌ Failed to close position {position.ticket}")
    
    def run_trade_tests(self):
        """Run comprehensive trade execution tests."""
        logging.info("=" * 60)
        logging.info("STARTING TRADE EXECUTION TESTS")
        logging.info("=" * 60)
        
        test_results = []
        
        # Test 1: Regular Buy Trade
        logging.info("\n" + "=" * 40)
        logging.info("TEST 1: REGULAR BUY TRADE")
        logging.info("=" * 40)
        result1 = self.test_trade_execution("buy", "regular")
        test_results.append(("Regular Buy", result1))
        
        time.sleep(3)  # Wait between trades
        
        # Test 2: Regular Sell Trade
        logging.info("\n" + "=" * 40)
        logging.info("TEST 2: REGULAR SELL TRADE")
        logging.info("=" * 40)
        result2 = self.test_trade_execution("sell", "regular")
        test_results.append(("Regular Sell", result2))
        
        time.sleep(3)  # Wait between trades
        
        # Test 3: Strong Buy Trade
        logging.info("\n" + "=" * 40)
        logging.info("TEST 3: STRONG BUY TRADE")
        logging.info("=" * 40)
        result3 = self.test_trade_execution("buy", "strong")
        test_results.append(("Strong Buy", result3))
        
        time.sleep(3)  # Wait between trades
        
        # Test 4: Strong Sell Trade
        logging.info("\n" + "=" * 40)
        logging.info("TEST 4: STRONG SELL TRADE")
        logging.info("=" * 40)
        result4 = self.test_trade_execution("sell", "strong")
        test_results.append(("Strong Sell", result4))
        
        # Wait a moment to see positions
        time.sleep(5)
        
        # Close all test positions
        logging.info("\n" + "=" * 40)
        logging.info("CLOSING TEST POSITIONS")
        logging.info("=" * 40)
        self.close_all_test_positions()
        
        # Print summary
        logging.info("\n" + "=" * 60)
        logging.info("TRADE EXECUTION TEST SUMMARY")
        logging.info("=" * 60)
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ PASSED" if result else "❌ FAILED"
            logging.info(f"{test_name}: {status}")
            if result:
                passed += 1
        
        logging.info(f"\nOverall Result: {passed}/{len(test_results)} tests passed")
        
        if passed == len(test_results):
            logging.info("🎉 ALL TRADE EXECUTION TESTS PASSED!")
            logging.info("The system can successfully open trades with TP and SL.")
        else:
            logging.info("⚠️ Some tests failed. Check the logs above for details.")
        
        return passed == len(test_results)

def main():
    """Main entry point."""
    try:
        tester = TradeExecutionTester()
        success = tester.run_trade_tests()
        
        if success:
            print("\n🎉 SUCCESS: All trade execution tests passed!")
            print("The MACD trading system can successfully open trades with TP and SL.")
        else:
            print("\n⚠️ WARNING: Some trade execution tests failed.")
            print("Check the logs for details.")
        
    except Exception as e:
        logging.error(f"Test failed with error: {e}")
        print(f"❌ ERROR: {e}")
    
    finally:
        mt5.shutdown()
        logging.info("MT5 connection closed")

if __name__ == "__main__":
    main()
