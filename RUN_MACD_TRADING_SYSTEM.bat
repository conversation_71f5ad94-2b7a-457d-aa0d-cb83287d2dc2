@echo off
title MACD Trading System - Auto Runner
color 0A

echo.
echo ============================================================
echo          MACD TRADING SYSTEM - AUTOMATIC RUNNER
echo ============================================================
echo.
echo AUTOMATIC STARTUP - NO USER INPUT REQUIRED
echo.
echo System will automatically:
echo 1. Test system for errors
echo 2. Test MT5 connection and symbol availability
echo 3. Start automated trading immediately
echo.
echo Symbol: DEX 900 DOWN Index
echo Timeframe: 15 minutes
echo Strategy: Dual MACD (Regular + Strong Signals)
echo.
echo ============================================================
echo                    IMPORTANT NOTICE
echo ============================================================
echo.
echo - Make sure MetaTrader 5 is RUNNING and LOGGED IN
echo - System assumes you are using a DEMO account
echo - The system will execute REAL TRADES automatically
echo - Press Ctrl+C at any time to stop the system
echo.
echo Starting in 3 seconds...
timeout /t 3 /nobreak >nul

REM Step 1: Test System for Errors
echo.
echo ============================================================
echo STEP 1: TESTING SYSTEM FOR ERRORS
echo ============================================================
echo.
echo Running comprehensive system tests...

call venv\Scripts\activate.bat
python test_system_errors.py

if errorlevel 1 (
    echo.
    echo ❌ SYSTEM TESTS FAILED!
    echo Please fix the errors above before proceeding.
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ System tests completed successfully!

REM Step 2: Test MT5 Connection (Automatic)
echo.
echo ============================================================
echo STEP 2: TESTING MT5 CONNECTION (AUTOMATIC)
echo ============================================================
echo.
echo Testing MT5 connection and symbol availability...
echo - MT5 terminal connection
echo - Account information
echo - Symbol availability (DEX 900 DOWN Index)
echo - Price data retrieval
echo - Trading permissions
python test_mt5_connection.py

if errorlevel 1 (
    echo.
    echo ❌ MT5 CONNECTION TEST FAILED!
    echo Please check MetaTrader 5 connection and try again.
    echo.
    echo System will continue attempting to connect...
    echo Press Ctrl+C to stop if needed.
    echo.
)

echo.
echo ✅ MT5 connection test completed!

REM Step 3: Start Trading Automatically
echo.
echo ============================================================
echo STEP 3: STARTING AUTOMATED TRADING
echo ============================================================
echo.
echo 🚀 STARTING AUTOMATED TRADING SYSTEM
echo.
echo The system will:
echo ✓ Monitor DEX 900 DOWN Index on 15-minute timeframe
echo ✓ Execute regular trades (0.01 lots, 10000 TP / 6000 SL) on MACD crossovers
echo ✓ Execute strong trades (0.01 lots, 30000 TP / 15000 SL) on trend-aligned signals
echo ✓ Log all activity to macd_trading.log
echo ✓ Run continuously until you stop it (Ctrl+C)
echo ✓ Automatically reconnect if MT5 connection is lost
echo.
echo ASSUMPTION: Using DEMO account (recommended for testing)
echo.
echo Starting trading system in 2 seconds...
timeout /t 2 /nobreak >nul

echo.
echo ============================================================
echo          MACD TRADING SYSTEM - NOW ACTIVE
echo ============================================================
echo.
echo System Status: RUNNING
echo Symbol: DEX 900 DOWN Index
echo Timeframe: 15 minutes
echo Mode: Automatic (No user input required)
echo.
echo Press Ctrl+C to stop trading at any time.
echo Check macd_trading.log for detailed logs.
echo.

REM Start the trading strategy
python macd_trading_strategy.py

REM If we reach here, the trading system has stopped
echo.
echo ============================================================
echo          TRADING SYSTEM STOPPED
echo ============================================================
echo.
echo The MACD trading system has been stopped.
echo.
echo Check the following for details:
echo - Console output above for any error messages
echo - macd_trading.log file for detailed trading logs
echo - MetaTrader 5 terminal for executed trades
echo.
echo Thank you for using the MACD Trading System!
echo.
pause
