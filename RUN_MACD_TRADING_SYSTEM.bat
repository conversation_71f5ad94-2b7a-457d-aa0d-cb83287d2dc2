@echo off
title MACD Trading System - Full System Runner
color 0A

echo.
echo ============================================================
echo          MACD TRADING SYSTEM - FULL SYSTEM RUNNER
echo ============================================================
echo.
echo This batch file will run the complete MACD trading system:
echo 1. Test system for errors
echo 2. Test MT5 connection and symbol availability
echo 3. Start automated trading (with confirmation)
echo.
echo Symbol: DEX 900 DOWN Index
echo Timeframe: 15 minutes
echo Strategy: Dual MACD (Regular + Strong Signals)
echo.
echo ============================================================
echo                    IMPORTANT WARNINGS
echo ============================================================
echo.
echo BEFORE RUNNING THIS SYSTEM:
echo - Ensure MetaTrader 5 is RUNNING and LOGGED IN
echo - Use a DEMO account for initial testing
echo - Monitor the first few trades closely
echo - Have sufficient margin in your account
echo.
echo The system will execute REAL TRADES automatically!
echo Press Ctrl+C at any time to stop the system.
echo.
pause

REM Step 1: Test System for Errors
echo.
echo ============================================================
echo STEP 1: TESTING SYSTEM FOR ERRORS
echo ============================================================
echo.
echo Running comprehensive system tests...

call venv\Scripts\activate.bat
python test_system_errors.py

if errorlevel 1 (
    echo.
    echo ❌ SYSTEM TESTS FAILED!
    echo Please fix the errors above before proceeding.
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ System tests completed successfully!
echo.
pause

REM Step 2: Test MT5 Connection
echo.
echo ============================================================
echo STEP 2: TESTING MT5 CONNECTION
echo ============================================================
echo.
echo IMPORTANT: Make sure MetaTrader 5 is running and logged in!
echo.
echo This will test:
echo - MT5 terminal connection
echo - Account information  
echo - Symbol availability (DEX 900 DOWN Index)
echo - Price data retrieval
echo - Trading permissions
echo.
set /p mt5_ready="Is MetaTrader 5 running and logged in? (y/N): "
if /i not "%mt5_ready%"=="y" (
    echo.
    echo Please start MetaTrader 5 and log in, then run this script again.
    pause
    exit /b 0
)

echo.
echo Testing MT5 connection...
python test_mt5_connection.py

if errorlevel 1 (
    echo.
    echo ❌ MT5 CONNECTION TEST FAILED!
    echo Please check MetaTrader 5 connection and try again.
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ MT5 connection test completed successfully!
echo.
pause

REM Step 3: Final Confirmation and Start Trading
echo.
echo ============================================================
echo STEP 3: START AUTOMATED TRADING
echo ============================================================
echo.
echo ⚠️  FINAL WARNING: AUTOMATED TRADING WILL START!
echo.
echo The system will:
echo ✓ Monitor DEX 900 DOWN Index on 15-minute timeframe
echo ✓ Execute regular trades (0.01 lots, 100 TP / 50 SL) on MACD crossovers
echo ✓ Execute strong trades (0.01 lots, 300 TP / 150 SL) on trend-aligned signals
echo ✓ Log all activity to macd_trading.log
echo ✓ Run continuously until you stop it (Ctrl+C)
echo.
echo Account Type Check:
set /p account_type="Are you using a DEMO account? (y/N): "
if /i not "%account_type%"=="y" (
    echo.
    echo ⚠️  WARNING: You indicated this is NOT a demo account!
    echo It is STRONGLY recommended to test on demo first.
    echo.
    set /p live_confirm="Are you absolutely sure you want to trade on a LIVE account? (y/N): "
    if /i not "%live_confirm%"=="y" (
        echo.
        echo Trading cancelled for safety. Please use a demo account first.
        pause
        exit /b 0
    )
)

echo.
echo Final Confirmation:
set /p final_confirm="Start automated MACD trading system? (y/N): "
if /i not "%final_confirm%"=="y" (
    echo.
    echo Trading cancelled by user.
    pause
    exit /b 0
)

echo.
echo ============================================================
echo          STARTING MACD TRADING SYSTEM
echo ============================================================
echo.
echo System Status: ACTIVE
echo Symbol: DEX 900 DOWN Index
echo Timeframe: 15 minutes
echo.
echo Press Ctrl+C to stop trading at any time.
echo Check macd_trading.log for detailed logs.
echo.
echo Starting in 3 seconds...
timeout /t 3 /nobreak >nul

REM Start the trading strategy
python macd_trading_strategy.py

REM If we reach here, the trading system has stopped
echo.
echo ============================================================
echo          TRADING SYSTEM STOPPED
echo ============================================================
echo.
echo The MACD trading system has been stopped.
echo.
echo Check the following for details:
echo - Console output above for any error messages
echo - macd_trading.log file for detailed trading logs
echo - MetaTrader 5 terminal for executed trades
echo.
echo Thank you for using the MACD Trading System!
echo.
pause
