@echo off
title MACD Trading System - Restart
color 0A

echo.
echo ============================================================
echo          MACD TRADING SYSTEM - RESTART
echo ============================================================
echo.
echo Restarting MACD Trading System...
echo.
echo Current Status:
echo - Symbol: DEX 900 DOWN Index
echo - Timeframe: 15 minutes
echo - Position Limits: 1 Regular + 1 Strong Signal max
echo - Regular Trades: 10000 TP / 6000 SL
echo - Strong Trades: 30000 TP / 15000 SL
echo.
echo The system will:
echo ✓ Check existing positions automatically
echo ✓ Resume monitoring for new signals
echo ✓ Respect position limits (no duplicate trades)
echo ✓ Log status updates every 5 minutes
echo.
echo Press Ctrl+C to stop trading at any time.
echo.

REM Activate virtual environment and start trading
call venv\Scripts\activate.bat
python macd_trading_strategy.py

echo.
echo ============================================================
echo Trading system stopped.
echo Check macd_trading.log for details.
echo ============================================================
pause
