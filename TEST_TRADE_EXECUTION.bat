@echo off
title MACD Trading System - Trade Execution Test
color 0A

echo.
echo ============================================================
echo          MACD TRADING SYSTEM - TRADE EXECUTION TEST
echo ============================================================
echo.
echo This test will verify if the system can open trades with
echo specified Take Profit and Stop Loss levels.
echo.
echo Tests to be performed:
echo 1. Regular Buy Trade  (10000 TP / 6000 SL)
echo 2. Regular Sell Trade (10000 TP / 6000 SL)
echo 3. Strong Buy Trade   (30000 TP / 15000 SL)
echo 4. Strong Sell Trade  (30000 TP / 15000 SL)
echo.
echo IMPORTANT:
echo - Make sure MetaTrader 5 is running and logged in
echo - Use DEMO account only for testing
echo - Test positions will be opened and closed automatically
echo - Check trade_execution_test.log for detailed results
echo.
echo Press any key to start the trade execution test...
pause

echo.
echo Starting trade execution test...
echo ============================================================

REM Activate virtual environment and run test
call venv\Scripts\activate.bat
python test_trade_execution.py

echo.
echo ============================================================
echo Trade execution test completed.
echo Check trade_execution_test.log for detailed results.
echo Also check your MT5 terminal for trade history.
echo ============================================================
pause
